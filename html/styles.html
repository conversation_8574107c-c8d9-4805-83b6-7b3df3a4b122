<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html,
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    min-height: 100vh;
    color: #e2e8f0;
    line-height: 1.6;
  }

  /* 背景装饰 */
  .background-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  }

  .bg-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    animation: float 8s ease-in-out infinite;
    filter: blur(1px);
  }

  .bg-circle-1 {
    width: 300px;
    height: 300px;
    top: -10%;
    left: -5%;
    animation-delay: 0s;
  }

  .bg-circle-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -5%;
    animation-delay: 3s;
  }

  .bg-circle-3 {
    width: 150px;
    height: 150px;
    bottom: -5%;
    left: 30%;
    animation-delay: 6s;
  }

  @keyframes float {

    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }

    50% {
      transform: translateY(-30px) rotate(180deg);
    }
  }

  /* 添加网格背景效果 */
  .main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: -1;
  }

  /* 主容器 */
  .main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 2rem 0rem 2rem;
    position: relative;
    z-index: 1;
  }

  /* 头部 */
  .header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
  }

  .main-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .ip-display {
    margin: 1.5rem 0;
    width: 100%;
    display: flex;
    justify-content: center;
    overflow: hidden;
  }

  .ip-value {
    display: inline-block;
    font-size: 1.8rem;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-family: 'Monaco', 'Menlo', monospace;
    letter-spacing: 1px;
    max-width: 90vw;
    word-break: break-all;
    text-align: center;
    overflow-wrap: break-word;
    hyphens: auto;
    transition: all 0.3s ease;
  }

  /* IPv6 地址特殊处理 */
  .ip-value.ipv6 {
    font-size: 1.4rem;
    letter-spacing: 0.5px;
    line-height: 1.2;
  }

  /* 顶部查询区域 */
  .top-query-section {
    margin-top: 1.5rem;
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .search-container:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }

  .search-input {
    flex: 1;
    padding: 0.7rem 1.2rem;
    border: none;
    background: transparent;
    color: white;
    font-size: 0.9rem;
    outline: none;
  }

  .search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  .search-btn {
    padding: 0.6rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0.2rem;
    width: 42px;
    height: 42px;
  }

  .search-btn:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  }

  /* 内容区域 */
  .content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
    align-items: start;
  }

  /* 面板通用样式 */
  .info-panel,
  .json-panel {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  /* 信息面板独立高度 */
  .info-panel {
    height: fit-content;
  }

  /* JSON 面板保持最小高度 */
  .json-panel {
    min-height: 400px;
  }

  .info-panel::before,
  .json-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
    animation: scan 3s infinite;
  }

  @keyframes scan {
    0% {
      left: -100%;
    }

    100% {
      left: 100%;
    }
  }

  .info-panel:hover,
  .json-panel:hover {
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  }

  .panel-header h2 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .panel-header h2::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 2px;
  }

  /* 信息网格 */
  .info-grid {
    display: grid;
    gap: 0.5rem;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(30, 41, 59, 0.6);
    border-radius: 10px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .info-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  }

  .info-item:hover {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateX(2px);
  }

  .info-label {
    font-weight: 500;
    color: #94a3b8;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .info-value {
    font-weight: 600;
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.8rem;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  /* 位置和捐助区域 */
  .location-section {
    margin-top: 1.25rem;
    padding-top: 1.25rem;
    border-top: 1px solid rgba(59, 130, 246, 0.2);
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  /* 按钮行布局 */
  .button-row {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .map-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    background: linear-gradient(135deg, #06b6d4, #3b82f6);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    justify-content: center;
    border: 1px solid rgba(59, 130, 246, 0.3);
  }

  .map-btn:hover {
    background: linear-gradient(135deg, #0891b2, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .sponsor-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
  }

  .sponsor-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    background: linear-gradient(135deg, #f59e0b, #ef4444);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(245, 158, 11, 0.3);
    flex: 1;
    justify-content: center;
    flex: 1;
    justify-content: center;
  }

  .sponsor-btn:hover {
    background: linear-gradient(135deg, #d97706, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }

  .sponsor-text {
    font-size: 0.7rem;
    color: #94a3b8;
    text-align: center;
  }

  .sponsor-text a {
    color: #3b82f6;
    text-decoration: none;
  }

  .sponsor-text a:hover {
    text-decoration: underline;
    color: #60a5fa;
  }

  /* 响应式设计 - 小屏幕上按钮垂直排列 */
  @media (max-width: 480px) {
    .button-row {
      flex-direction: column;
      gap: 0.5rem;
    }

    .map-btn,
    .sponsor-btn {
      width: 100%;
    }
  }

  /* JSON 面板 */
  .copy-btn {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(16, 185, 129, 0.3);
  }

  .copy-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  .json-container {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1.25rem;
    overflow-x: auto;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .json-content {
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.8rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  /* JSON 语法高亮 */
  .json-key {
    color: #60a5fa;
  }

  .json-string {
    color: #34d399;
  }

  .json-number {
    color: #fbbf24;
  }

  .json-boolean {
    color: #f87171;
  }

  .json-null {
    color: #9ca3af;
  }

  .json-punctuation {
    color: #e2e8f0;
  }

  /* API 使用说明 */
  .api-section {
    padding-top: 1.25rem;
    border-top: 1px solid rgba(59, 130, 246, 0.2);
  }

  .api-section h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .api-section h3::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 2px;
  }

  .api-examples {
    display: grid;
    gap: 0.5rem;
  }

  .api-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.75rem;
    background: rgba(30, 41, 59, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
  }

  .api-item:hover {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(59, 130, 246, 0.3);
  }

  .api-label {
    font-weight: 500;
    color: #94a3b8;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .api-code {
    background: rgba(0, 0, 0, 0.4);
    color: #e2e8f0;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.7rem;
    word-break: break-all;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  /* 地图模态框 */
  .map-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(5px);
  }

  .map-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .map-container {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
  }

  .map-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .close-btn:hover {
    background: #e2e8f0;
    color: #2d3748;
  }

  .map-frame {
    width: 100%;
    height: 400px;
    border: none;
  }

  /* 底部 */
  .footer {
    text-align: center;
    color: #94a3b8;
    padding: 1.5rem 0;
    border-top: 1px solid rgba(59, 130, 246, 0.2);
    font-size: 0.8rem;
  }

  .footer a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .footer a:hover {
    color: #60a5fa;
    text-decoration: underline;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .main-container {
      padding: 1rem;
    }

    .main-title {
      font-size: 1.6rem;
    }

    .ip-value {
      font-size: 1.4rem;
      padding: 0.6rem 1.2rem;
      max-width: 95vw;
      letter-spacing: 0.5px;
    }

    .content-wrapper {
      grid-template-columns: 1fr;
      gap: 1.2rem;
    }

    .info-panel,
    .json-panel {
      padding: 1.2rem;
    }

    .top-query-section {
      max-width: 100%;
    }

    .button-row {
      flex-direction: column;
      gap: 0.5rem;
    }

    .map-container {
      width: 95%;
      max-height: 70vh;
    }

    .map-frame {
      height: 300px;
    }
  }

  @media (max-width: 480px) {
    .main-title {
      font-size: 1.4rem;
    }

    .ip-value {
      font-size: 1.1rem;
      padding: 0.5rem 0.8rem;
      letter-spacing: 0.2px;
      max-width: 98vw;
      line-height: 1.3;
    }

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.2rem;
    }

    .info-panel,
    .json-panel {
      padding: 1rem;
    }

    .map-frame {
      height: 250px;
    }
  }

  /* 超小屏幕优化 */
  @media (max-width: 320px) {
    .ip-value {
      font-size: 1rem;
      padding: 0.4rem 0.6rem;
      letter-spacing: 0px;
      word-spacing: -2px;
    }
  }

  /* 地图模态框 */
  .map-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(5px);
  }

  .map-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .map-container {
    background: rgba(15, 23, 42, 0.95);
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.3);
  }

  .map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    background: rgba(30, 41, 59, 0.8);
  }

  .map-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #e2e8f0;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #94a3b8;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .close-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #e2e8f0;
  }

  .map-frame {
    width: 100%;
    height: 400px;
    border: none;
  }
</style>