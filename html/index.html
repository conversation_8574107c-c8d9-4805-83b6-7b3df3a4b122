<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8" />
  <title>你的 IP | 楠格</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="{{ .Host }} • IP 地址查询 &mdash; 查询IP信息的最佳工具。" />
  <link rel="icon" href="favicon.ico" type="image/x-icon" />
  <link rel="canonical" href="https://www.openstreetmap.org/" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

  {{ template "script.html" . }} {{ template "styles.html" . }}
</head>

<body>
  <!-- 背景装饰 -->
  <div class="background-decoration">
    <div class="bg-circle bg-circle-1"></div>
    <div class="bg-circle bg-circle-2"></div>
    <div class="bg-circle bg-circle-3"></div>
  </div>

  <!-- 主要内容 -->
  <div class="main-container">
    <!-- 头部区域 -->
    <header class="header">
      <div class="header-content">
        <h1 class="main-title">IP 地址查询</h1>
        <div class="ip-display">
          <span class="ip-value">{{ .IP }}</span>
        </div>

        <!-- 顶部查询框和主题切换 -->
        <div class="top-query-section">
          <div class="search-container">
            <input id="ipInput" type="text" placeholder="输入 IP 地址查询其他位置" class="search-input"
              onkeyup="updateIP(this.value)" />
            <button type="button" class="search-btn" onclick="navigate()">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.35-4.35" />
              </svg>
            </button>
          </div>
          <!-- 主题切换按钮 -->
          <button type="button" class="theme-toggle-btn" onclick="toggleTheme()" title="切换主题">
            <svg class="theme-icon sun-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="5"/>
              <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
            </svg>
            <svg class="theme-icon moon-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <!-- 左侧信息框 -->
      <div class="info-panel">
        <div class="panel-header">
          <h2>IP 详细信息</h2>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">IP 地址</span>
            <span class="info-value">{{ .IP }}</span>
          </div>
          {{ if .IPDecimal }}
          <div class="info-item">
            <span class="info-label">Decimal</span>
            <span class="info-value">{{ .IPDecimal }}</span>
          </div>
          {{ end }}
          {{ if .Country }}
          <div class="info-item">
            <span class="info-label">国家/地区</span>
            <span class="info-value">{{ .Country }}</span>
          </div>
          {{ end }}
          {{ if .CountryCode }}
          <div class="info-item">
            <span class="info-label">国家代码</span>
            <span class="info-value">{{ .CountryCode }}</span>
          </div>
          {{ end }}
          {{ if .RegionName }}
          <div class="info-item">
            <span class="info-label">省/州</span>
            <span class="info-value">{{ .RegionName }}</span>
          </div>
          {{ end }}
          {{ if .City }}
          <div class="info-item">
            <span class="info-label">城市</span>
            <span class="info-value">{{ .City }}</span>
          </div>
          {{ end }}
          {{ if .PostalCode }}
          <div class="info-item">
            <span class="info-label">邮编</span>
            <span class="info-value">{{ .PostalCode }}</span>
          </div>
          {{ end }}
          {{ if .Latitude }}
          <div class="info-item">
            <span class="info-label">纬度</span>
            <span class="info-value">{{ .Latitude }}</span>
          </div>
          {{ end }}
          {{ if .Longitude }}
          <div class="info-item">
            <span class="info-label">经度</span>
            <span class="info-value">{{ .Longitude }}</span>
          </div>
          {{ end }}
          {{ if .Timezone }}
          <div class="info-item">
            <span class="info-label">时区</span>
            <span class="info-value">{{ .Timezone }}</span>
          </div>
          {{ end }}
          {{ if .ASN }}
          <div class="info-item">
            <span class="info-label">ASN</span>
            <span class="info-value">{{ .ASN }}</span>
          </div>
          {{ end }}
          {{ if .ISP }}
          <div class="info-item">
            <span class="info-label">服务商</span>
            <span class="info-value">{{ .ISP }}</span>
          </div>
          {{ end }}
          {{ if .ORG }}
          <div class="info-item">
            <span class="info-label">所属组织</span>
            <span class="info-value">{{ .ORG }}</span>
          </div>
          {{ end }}
          {{ if .ConnectionType }}
          <div class="info-item">
            <span class="info-label">网络类型</span>
            <span class="info-value">{{ .ConnectionType }}</span>
          </div>
          {{ end }}
          {{ if .Hostname }}
          <div class="info-item">
            <span class="info-label">主机名</span>
            <span class="info-value">{{ .Hostname }}</span>
          </div>
          {{ end }}
        </div>

        <!-- 地图和捐助区域 -->
        <div class="location-section">
          <div class="button-row">
            {{ if and .Latitude .Longitude }}
            <button class="map-btn" onclick="showMap()">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
              </svg>
              查看地图
            </button>
            {{ end }}

            <a href="https://donate.stripe.com/28obMffW78Pogb6145" target="_blank" class="sponsor-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
              </svg>
              支持项目
            </a>
          </div>
        </div>
      </div>

      <!-- 右侧 JSON 预览框 -->
      <div class="json-panel">
        <div class="panel-header">
          <h2>JSON 响应</h2>
          <button class="copy-btn" onclick="copyJSON()" title="复制 JSON">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
            </svg>
          </button>
        </div>
        <div class="json-container">
          <pre id="jsonOutput" class="json-content">{{ .JSON }}</pre>
        </div>

        <!-- API 使用说明 -->
        <div class="api-section">
          <h3>API 使用</h3>
          <div class="api-examples">
            <div class="api-item">
              <span class="api-label">获取完整信息:</span>
              <code class="api-code"><span class="json-key">curl</span> <span class="json-string">{{ .Host }}/json</span></code>
            </div>
            <div class="api-item">
              <span class="api-label">查询指定 IP:</span>
              <code class="api-code"><span class="json-key">curl</span> <span class="json-string">{{ .Host }}/json</span>?<span class="json-boolean">ip</span>=<span class="json-number">*******</span></code>
            </div>
            <div class="api-item">
              <span class="api-label">指定语言:</span>
              <code class="api-code"><span class="json-key">curl</span> <span class="json-string">{{ .Host }}/json</span>?<span class="json-boolean">lang</span>=<span class="json-number">zh</span>  <span class="json-null"># 中文</span><br><span class="json-key">curl</span> <span class="json-string">{{ .Host }}/json</span>?<span class="json-boolean">lang</span>=<span class="json-number">en</span>  <span class="json-null"># 英文</span></code>
            </div>
            <div class="api-item">
              <span class="api-label">端口检测:</span>
              <code class="api-code"><span class="json-key">curl</span> <span class="json-string">{{ .Host }}/json</span>?<span class="json-boolean">ip</span>=<span class="json-number">*******</span>&<span class="json-boolean">port</span>=<span class="json-number">80</span></code>
            </div>
            <div class="api-item">
              <span class="api-label">组合参数:</span>
              <code class="api-code"><span class="json-key">curl</span> <span class="json-string">{{ .Host }}/json</span>?<span class="json-boolean">ip</span>=<span class="json-number">*******</span>&<span class="json-boolean">lang</span>=<span class="json-number">en</span>&<span class="json-boolean">port</span>=<span class="json-number">443</span></code>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <footer class="footer">
      <p>
        本站由 <a href="https://www.maxmind.com" target="_blank">MaxMind</a> ·
        <a href="https://www.cz88.net" target="_blank">纯真CZ88</a> 提供数据支持
      </p>
      <p>本站由 <a href="https://www.nange.cn" target="_blank">楠格</a> 运营维护</p>
    </footer>

    <!-- 地图模态框 -->
    <div id="mapModal" class="map-modal" onclick="closeMap()">
      <div class="map-container" onclick="event.stopPropagation()">
        <div class="map-header">
          <h3>IP 位置地图</h3>
          <button class="close-btn" onclick="closeMap()">&times;</button>
        </div>
        <div id="mapFrame" class="map-frame"></div>
      </div>
    </div>
  </div>
</body>

</html>