<script lang="text/javascript">
  let host = "{{ .Host }}";
  let jsonObj = "{{ .JSON }}";
  let data = JSON.parse(jsonObj);
  let ip = '';
  let ipQuery = '';

  window.onload = (event) => {
    // 初始化主题
    initTheme();
    // 格式化 JSON 显示
    formatJSON();
    // 检查并优化 IP 显示
    optimizeIPDisplay();
    // 高亮 API 代码
    highlightApiCode();
  }

  // 主题管理
  function initTheme() {
    // 默认使用亮色主题
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
  }

  function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  }

  function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);

    // 更新主题切换按钮的提示文本
    const themeBtn = document.querySelector('.theme-toggle-btn');
    if (themeBtn) {
      themeBtn.title = theme === 'light' ? '切换到暗色模式' : '切换到亮色模式';
    }
  }

  function optimizeIPDisplay() {
    const ipValueElement = document.querySelector('.ip-value');
    if (ipValueElement) {
      const ipText = ipValueElement.textContent;

      // 检测 IPv6 地址
      if (ipText.includes(':') && ipText.length > 15) {
        ipValueElement.classList.add('ipv6');
      }

      // 检测超长 IP 地址
      if (ipText.length > 20) {
        ipValueElement.style.fontSize = '1.2rem';
        ipValueElement.style.letterSpacing = '0.2px';
      }
    }
  }

  function formatJSON() {
    const jsonOutput = document.getElementById('jsonOutput');
    if (jsonOutput) {
      try {
        const formatted = JSON.stringify(JSON.parse(jsonObj), null, 2);
        const highlighted = syntaxHighlight(formatted);
        jsonOutput.innerHTML = highlighted;
      } catch (e) {
        jsonOutput.textContent = jsonObj;
      }
    }
  }

  function syntaxHighlight(json) {
    json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
      var cls = 'json-number';
      if (/^"/.test(match)) {
        if (/:$/.test(match)) {
          cls = 'json-key';
        } else {
          cls = 'json-string';
        }
      } else if (/true|false/.test(match)) {
        cls = 'json-boolean';
      } else if (/null/.test(match)) {
        cls = 'json-null';
      }
      return '<span class="' + cls + '">' + match + '</span>';
    });
  }

  function highlightApiCode() {
    const apiCodes = document.querySelectorAll('.api-code');
    apiCodes.forEach(code => {
      let content = code.textContent || code.innerText;

      // 高亮 curl 命令
      content = content.replace(/\bcurl\b/g, '<span class="json-key">curl</span>');

      // 高亮 URL 部分（不包含参数）
      content = content.replace(/(https?:\/\/[^\s?]+)/g, '<span class="json-string">$1</span>');

      // 高亮参数
      content = content.replace(/([?&])([^=\s]+)(=)([^&\s#]+)/g, '$1<span class="json-boolean">$2</span>$3<span class="json-number">$4</span>');

      // 高亮注释
      content = content.replace(/(#[^\n]*)/g, '<span class="json-null">$1</span>');

      code.innerHTML = content;
    });
  }

  function copyJSON() {
    // 获取原始 JSON 内容，不包含 HTML 标签
    const jsonContent = JSON.stringify(JSON.parse(jsonObj), null, 2);
    
    if (navigator.clipboard) {
      navigator.clipboard.writeText(jsonContent).then(() => {
        showCopySuccess();
      }).catch(() => {
        fallbackCopy(jsonContent);
      });
    } else {
      fallbackCopy(jsonContent);
    }
  }

  function fallbackCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      showCopySuccess();
    } catch (err) {
      console.error('复制失败:', err);
    }
    
    document.body.removeChild(textArea);
  }

  function showCopySuccess() {
    const copyBtn = document.querySelector('.copy-btn');
    const originalHTML = copyBtn.innerHTML;

    // 创建成功提示图标
    const successIcon = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="20,6 9,17 4,12"/>
      </svg>
    `;

    copyBtn.innerHTML = successIcon;
    copyBtn.style.background = '#10b981';
    copyBtn.title = '已复制!';

    setTimeout(() => {
      copyBtn.innerHTML = originalHTML;
      copyBtn.style.background = '';
      copyBtn.title = '复制 JSON';
    }, 2000);
  }

  function navigate() {
    const ipInput = document.getElementById('ipInput');
    const inputValue = ipInput.value.trim();
    
    if (inputValue) {
      // 简单的 IP 地址验证
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
      
      if (ipRegex.test(inputValue) || ipv6Regex.test(inputValue)) {
        window.location.href = `/?ip=${encodeURIComponent(inputValue)}`;
      } else {
        alert('请输入有效的 IP 地址');
        ipInput.focus();
      }
    } else {
      alert('请输入 IP 地址');
      ipInput.focus();
    }
  }

  function updateIP(value) {
    ip = value.trim();
  }

  // 显示地图
  function showMap() {
    const modal = document.getElementById('mapModal');
    const mapFrame = document.getElementById('mapFrame');
    
    if (data.latitude && data.longitude) {
      // 使用 OpenStreetMap 嵌入地图
      const lat = data.latitude;
      const lon = data.longitude;
      const mapUrl = `https://www.openstreetmap.org/export/embed.html?bbox=${lon-0.01},${lat-0.01},${lon+0.01},${lat+0.01}&layer=mapnik&marker=${lat},${lon}`;
      
      mapFrame.innerHTML = `<iframe src="${mapUrl}" width="100%" height="100%" frameborder="0"></iframe>`;
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
  }

  // 关闭地图
  function closeMap() {
    const modal = document.getElementById('mapModal');
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
  }

  // 回车键查询
  document.addEventListener('DOMContentLoaded', function() {
    const ipInput = document.getElementById('ipInput');
    if (ipInput) {
      ipInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          navigate();
        }
      });
    }

    // ESC 键关闭地图
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeMap();
      }
    });
  });
</script>
