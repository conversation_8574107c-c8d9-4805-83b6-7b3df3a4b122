name: Build GeoIP Image And Push

on:
  workflow_dispatch:
  push:
    branches:
      - "master"
    paths-ignore:
      - "*.md"
      - ".*"

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master

      - name: Log In To The GHCR
        uses: docker/login-action@master
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login To Docker Hub
        uses: docker/login-action@master
        with:
          username: ${{ secrets.DOC_USER }}
          password: ${{ secrets.DOC_PAT }}    

      - name: Log In To The AliyunCS
        uses: docker/login-action@master
        with:
          registry: registry.cn-shanghai.aliyuncs.com
          username: ${{ secrets.ALI_USER }}
          password: ${{ secrets.ALI_PAT }}
      
      - name: Set Up QEMU
        uses: docker/setup-qemu-action@v1
        
      - name: Set Up Docker Buildx
        uses: docker/setup-buildx-action@v1
      
      - name: Set Up Image Name
        run: |
          GHRC_IMAGE_NAME=$(echo "ghcr.io/${{ github.repository_owner }}/geoip" | tr '[:upper:]' '[:lower:]')
          DOC_IMAGE_NAME=$(echo "nangle/geoip" | tr '[:upper:]' '[:lower:]')
          if [ ${{ github.repository_owner }} = "xOS" ]
            then ALI_IMAGE_NAME=$(echo "registry.cn-shanghai.aliyuncs.com/dns/geoip")
            else ALI_IMAGE_NAME=$(echo "registry.cn-shanghai.aliyuncs.com/${{ github.repository_owner }}/geoip" | tr '[:upper:]' '[:lower:]')
          fi
          echo "::set-output name=GHRC_IMAGE_NAME::$GHRC_IMAGE_NAME"
          echo "::set-output name=ALI_IMAGE_NAME::$ALI_IMAGE_NAME"
          echo "::set-output name=DOC_IMAGE_NAME::$DOC_IMAGE_NAME"
        id: image-name
        
      - name: Build Dasbboard Image And Push
        uses: docker/build-push-action@v2
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64,linux/arm,linux/386,linux/s390x
          push: true
          tags: |
            ${{ steps.image-name.outputs.ALI_IMAGE_NAME }}
            ${{ steps.image-name.outputs.DOC_IMAGE_NAME }}
            ${{ steps.image-name.outputs.GHRC_IMAGE_NAME }}
