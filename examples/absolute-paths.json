{"listen_address": ":1212", "template_dir": "/Users/<USER>/GitHub/GeoIP/html", "cache_size": 2000, "trusted_headers": ["X-Real-IP", "X-Forwarded-For"], "enable_reverse_lookup": true, "enable_port_lookup": true, "enable_profiling": false, "databases": {"country": "/Users/<USER>/GitHub/GeoIP/data/GeoLite2-Country.mmdb", "city": "/Users/<USER>/GitHub/GeoIP/data/GeoLite2-City.mmdb", "asn": "/Users/<USER>/GitHub/GeoIP/data/GeoLite2-ASN.mmdb", "isp": "/Users/<USER>/GitHub/GeoIP/data/GeoIP2-ISP.mmdb", "connection_type": "/Users/<USER>/GitHub/GeoIP/data/GeoIP2-Connection-Type.mmdb", "ip2proxy": "/Users/<USER>/GitHub/GeoIP/data/IP2PROXY-LITE-PX12.BIN", "qqwry": "/Users/<USER>/GitHub/GeoIP/data/qqwry.ipdb"}, "hybrid_mode": true, "auto_detect": false}